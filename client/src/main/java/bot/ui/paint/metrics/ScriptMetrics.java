package bot.ui.paint.metrics;

import bot.ui.paint.PaintMetric;
import bot.util.stats.XPTracker;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Simple script-controlled metrics tracker.
 * Scripts manually update all metrics based on game events.
 * Paint system only displays what scripts provide.
 */
public class ScriptMetrics {
    
    private final long startTime;
    private final XPTracker xpTracker;
    private final java.util.Set<Integer> trackedSkills = new java.util.HashSet<>();
    
    // Simple counters - scripts control everything
    private final Map<String, Integer> counters = new ConcurrentHashMap<>();
    // Track maximum values reached for each counter to show rates even when current count is 0
    private final Map<String, Integer> maxCounters = new ConcurrentHashMap<>();
    private String currentStatus = "Starting...";

    /**
     * Creates a new metrics tracker.
     */
    public ScriptMetrics() {
        this.startTime = System.currentTimeMillis();
        this.xpTracker = new XPTracker();
    }
    
    /**
     * Creates a new metrics tracker for specific skills.
     * 
     * @param skillIds The skill IDs to track XP for
     */
    public ScriptMetrics(int... skillIds) {
        this.startTime = System.currentTimeMillis();
        this.xpTracker = new XPTracker(skillIds);
        for (int skillId : skillIds) {
            trackedSkills.add(skillId);
        }
    }
    
    // ===== SIMPLE COUNTER METHODS =====
    
    /**
     * Increment a counter by 1.
     */
    public void increment(String counterName) {
        counters.merge(counterName, 1, Integer::sum);
        updateMaxCounter(counterName);
    }

    /**
     * Increment a counter by a specific amount.
     */
    public void increment(String counterName, int amount) {
        counters.merge(counterName, amount, Integer::sum);
        updateMaxCounter(counterName);
    }
    
    /**
     * Set a counter to a specific value.
     */
    public void set(String counterName, int value) {
        counters.put(counterName, value);
        updateMaxCounter(counterName);
    }

    /**
     * Updates the maximum value reached for a counter.
     * This allows us to show rate metrics even when current count is 0.
     */
    private void updateMaxCounter(String counterName) {
        int currentValue = counters.getOrDefault(counterName, 0);
        maxCounters.merge(counterName, currentValue, Integer::max);
    }

    /**
     * Determines whether to show a count metric based on counter type.
     * Inventory tracking counters (ending with "_in_inventory") only show when > 0.
     * Total/cumulative counters always show if they've ever been incremented.
     */
    private boolean shouldShowCountMetric(String counterName, int currentCount, int maxCount) {
        // Inventory tracking counters - only show when current count > 0
        if (counterName.endsWith("_in_inventory")) {
            return currentCount > 0;
        }

        // Total/cumulative counters - ALWAYS show if they've ever been incremented,
        // even if they've been reset back to 0
        return maxCount > 0;
    }
    
    /**
     * Get the current value of a counter.
     */
    public int get(String counterName) {
        return counters.getOrDefault(counterName, 0);
    }
    
    /**
     * Reset a counter to 0.
     */
    public void reset(String counterName) {
        counters.put(counterName, 0);
    }
    
    // ===== STATUS AND TIME =====
    
    /**
     * Set the current script status.
     */
    public void setStatus(String status) {
        this.currentStatus = status;
    }
    
    /**
     * Get the current script status.
     */
    public String getStatus() {
        return currentStatus;
    }
    
    /**
     * Get elapsed time since script started.
     */
    public long getElapsedTime() {
        return System.currentTimeMillis() - startTime;
    }
    
    /**
     * Get the XP tracker instance.
     */
    public XPTracker getXPTracker() {
        return xpTracker;
    }
    
    // ===== PAINT METRICS GENERATION =====
    
    /**
     * Generate paint metrics for display.
     * Scripts can override this method for custom metric display,
     * or use the default implementation which shows all counters + XP.
     */
    public Map<String, PaintMetric> generatePaintMetrics() {
        Map<String, PaintMetric> paintMetrics = new HashMap<>();
        
        // Runtime
        long startTime = System.currentTimeMillis() - getElapsedTime();
        paintMetrics.put("runtime", MetricsHelper.createRuntimeMetric(startTime));
        
        // Status
        paintMetrics.put("status", MetricsHelper.createStatusMetric(currentStatus));
        
        // All counters with rates
        // Use maxCounters to ensure we show rate metrics for any counter that has had activity
        for (String name : maxCounters.keySet()) {
            int count = counters.getOrDefault(name, 0);
            int maxCount = maxCounters.get(name);

            // Determine category based on name
            PaintMetric.MetricCategory category = getCategory(name);

            // Show count metric based on counter type
            if (shouldShowCountMetric(name, count, maxCount)) {
                paintMetrics.put(name, new PaintMetric(
                    formatName(name),
                    MetricsHelper.formatNumber(count),
                    category
                ));
            }

            // Always show rate metric if there has been any activity (maxCount > 0)
            if (maxCount > 0) {
                double rate = MetricsHelper.calculateRate(count, getElapsedTime());
                paintMetrics.put(name + "_rate", new PaintMetric(
                    formatName(name) + "/Hour",
                    MetricsHelper.formatRate(rate),
                    category
                ));
            }
        }
        
        // XP metrics for tracked skills
        for (int skillId : trackedSkills) {
            int xpGained = xpTracker.getXPGained(skillId);
            if (xpGained > 0) {
                String skillName = xpTracker.getSkillName(skillId);
                
                paintMetrics.put("xp_" + skillName.toLowerCase(), new PaintMetric(
                    skillName + " XP",
                    MetricsHelper.formatNumber(xpGained),
                    PaintMetric.MetricCategory.EXPERIENCE
                ));

                paintMetrics.put("xp_rate_" + skillName.toLowerCase(), new PaintMetric(
                    skillName + " XP/Hour",
                    MetricsHelper.formatRate(xpTracker.getXPPerHour(skillId)),
                    PaintMetric.MetricCategory.EXPERIENCE
                ));
            }
        }
        
        return paintMetrics;
    }
    
    /**
     * Determine category based on counter name.
     */
    private PaintMetric.MetricCategory getCategory(String name) {
        String lower = name.toLowerCase();
        if (lower.contains("bank")) return PaintMetric.MetricCategory.BANKING;
        if (lower.contains("kill") || lower.contains("combat")) return PaintMetric.MetricCategory.COMBAT;
        if (lower.contains("item") || lower.contains("log") || lower.contains("ore") || 
            lower.contains("bar") || lower.contains("platebody") || lower.contains("feather") || 
            lower.contains("bone")) return PaintMetric.MetricCategory.ITEMS;
        return PaintMetric.MetricCategory.GENERAL;
    }
    
    /**
     * Format counter name for display.
     */
    private String formatName(String name) {
        // Convert snake_case to Title Case
        String[] words = name.replace("_", " ").toLowerCase().split(" ");
        StringBuilder result = new StringBuilder();
        for (String word : words) {
            if (word.length() > 0) {
                result.append(Character.toUpperCase(word.charAt(0)))
                      .append(word.substring(1))
                      .append(" ");
            }
        }
        return result.toString().trim();
    }
}
